document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('companyProfileForm');
    const submitBtn = document.querySelector('.submit-btn');
    const skipBtn = document.querySelector('.skip-btn');
    
    // Load basic info from previous page
    const basicInfo = JSON.parse(localStorage.getItem('basicInfo') || '{}');
    
    // Pre-fill form with basic info if available
    if (basicInfo.country) {
        document.getElementById('country').value = basicInfo.country;
    }
    if (basicInfo.companyName) {
        document.getElementById('companyName').value = basicInfo.companyName;
    }
    if (basicInfo.phone) {
        document.getElementById('phone').value = basicInfo.phone;
    }
    if (basicInfo.whatsapp) {
        document.getElementById('whatsapp').value = basicInfo.whatsapp;
    }
    
    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // Validate required fields
        const requiredFields = ['firstName', 'lastName', 'position', 'country', 'companyName', 'businessType', 'email', 'registrationNumber'];
        let isValid = true;
        
        requiredFields.forEach(field => {
            const input = document.getElementById(field);
            if (!data[field] || data[field].trim() === '') {
                input.style.borderColor = '#ff4757';
                isValid = false;
            } else {
                input.style.borderColor = '#ddd';
            }
        });
        
        // Validate email format
        const emailInput = document.getElementById('email');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (data.email && !emailRegex.test(data.email)) {
            emailInput.style.borderColor = '#ff4757';
            isValid = false;
        }
        
        if (!isValid) {
            alert('Please fill in all required fields correctly.');
            return;
        }
        
        // Combine with basic info
        const completeData = { ...basicInfo, ...data };
        
        // Store complete data
        localStorage.setItem('completeProfile', JSON.stringify(completeData));
        
        // Show success message
        alert('Company profile submitted successfully! You will receive 90-day membership benefits.');
        
        // Here you would typically send data to server
        console.log('Complete profile data:', completeData);
        
        // Redirect or close modal
        window.location.href = 'success.html';
    });
    
    // Handle skip button
    skipBtn.addEventListener('click', function() {
        const confirmSkip = confirm('Are you sure you want to skip the company profile? You will miss out on 90-day membership benefits.');
        
        if (confirmSkip) {
            // Store basic info only
            localStorage.setItem('completeProfile', JSON.stringify(basicInfo));
            
            alert('Profile setup skipped. You can complete it later in your account settings.');
            
            // Redirect to main page or dashboard
            window.location.href = 'dashboard.html';
        }
    });
    
    // Clear error styling on input
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            this.style.borderColor = '#ddd';
        });
    });
    
    // Handle file uploads
    const fileInputs = form.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const files = this.files;
            if (files.length > 0) {
                const fileNames = Array.from(files).map(file => file.name).join(', ');
                console.log(`Selected files for ${this.name}:`, fileNames);
                
                // You could add visual feedback here
                this.style.borderColor = '#00d4aa';
            }
        });
    });
    
    // Add form validation feedback
    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        field.style.borderColor = '#ff4757';
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.color = '#ff4757';
        errorDiv.style.fontSize = '12px';
        errorDiv.style.marginTop = '5px';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }
    
    function clearFieldError(fieldId) {
        const field = document.getElementById(fieldId);
        field.style.borderColor = '#ddd';
        
        const errorMessage = field.parentNode.querySelector('.error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }
    
    // Real-time validation for email
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
        const email = this.value.trim();
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            showFieldError('email', 'Please enter a valid email address');
        } else {
            clearFieldError('email');
        }
    });
    
    // Add modal close functionality (if needed)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const confirmClose = confirm('Are you sure you want to close? Your progress will be lost.');
            if (confirmClose) {
                window.location.href = 'index.html';
            }
        }
    });
});

// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'transparent';
        });
    });
    
    // Add loading state to submit button
    const submitBtn = document.querySelector('.submit-btn');
    const originalText = submitBtn.textContent;
    
    form.addEventListener('submit', function() {
        submitBtn.textContent = 'Submitting...';
        submitBtn.disabled = true;
        
        // Reset after a delay (in real app, this would be after server response)
        setTimeout(() => {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
});
